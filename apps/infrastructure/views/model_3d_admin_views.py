"""3D Model Admin Views"""

from requests.exceptions import HTTP<PERSON>rror, ConnectionError, RequestException
from urllib3.exceptions import TimeoutError
from datetime import date
from django import forms
from django.contrib import admin
from django.contrib import messages
from django.contrib.auth import get_user_model
User = get_user_model()
from django.contrib.auth.decorators import login_required
from django.db import models
from django.db.models import Avg
from django.db.models import Q
from django.http import HttpResponse
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.shortcuts import redirect
from django.shortcuts import render
from rest_framework import permissions
from rest_framework import status
from rest_framework.response import Response
from typing import Any
from typing import List
import json
import re
import time

Django views for managing 3D models, procedural generation, and symbol mappings.
Provides comprehensive administrative interface for managing 3D asset libraries,
procedural model generation templates, and symbol-to-3D model mappings.

Features:
- 3D model library management and approval workflows
- Procedural model generation from templates
- Symbol-to-3D model mapping administration
- Bulk operations and search functionality
- Generation job monitoring and statistics
"""

from __future__ import annotations

import json
from typing import TYPE_CHECKING, Any

from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.db.models import Avg, Q
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.utils.decorators import method_decorator
from django.views.decorators.http import require_http_methods
from django.views.generic import ListView

if TYPE_CHECKING:
    from django.contrib.auth.models import AbstractUser
    from django.db.models import QuerySet

from apps.infrastructure.models import (
    ModelGenerationJob,
    ModelOptimizationProfile,
    ProceduralModelTemplate,
    ThreeDModelLibrary,
    UtilitySymbol3DMapping,
)
from apps.infrastructure.services.procedural_3d_generation import procedural_generator

# =============================================================================
# DECORATORS AND PERMISSIONS
# =============================================================================


def is_3d_admin(user: AbstractUser) -> bool:
    """Check if user has 3D model admin permissions.

    Args:
    ----
        user: User to check permissions for

    Returns:
    -------
        True if user has 3D admin permissions

    """
    return user.is_staff or user.groups.filter(name="3d_model_admins").exists()


# =============================================================================
# 3D MODEL LIBRARY VIEWS
# =============================================================================


@method_decorator([login_required, user_passes_test(is_3d_admin)], name="dispatch")
class ModelLibraryListView(ListView):
    """List view for 3D model library.

    Provides comprehensive listing of 3D models with search, filtering,
    and pagination capabilities for administrative management.
    """

    model = ThreeDModelLibrary
    template_name: str = "infrastructure/admin/3d_models/model_library_list.html"
    context_object_name: str = "models"
    paginate_by: int = 25

    def get_queryset(self) -> QuerySet[ThreeDModelLibrary]:
        """Get filtered and searched queryset of 3D models.

        Returns
        -------
            Filtered queryset based on search and filter parameters

        """
        queryset = ThreeDModelLibrary.objects.select_related("created_by")

        # Search functionality
        search = self.request.GET.get("search")
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(description__icontains=search) | Q(tags__icontains=search),
            )

        # Filter by model type
        model_type = self.request.GET.get("model_type")
        if model_type:
            queryset = queryset.filter(model_type=model_type)

        # Filter by utility category
        utility_category = self.request.GET.get("utility_category")
        if utility_category:
            queryset = queryset.filter(utility_category=utility_category)

        # Filter by quality level
        quality_level = self.request.GET.get("quality_level")
        if quality_level:
            queryset = queryset.filter(quality_level=quality_level)

        # Filter by approval status
        approval_status = self.request.GET.get("approval_status")
        if approval_status == "approved":
            queryset = queryset.filter(is_approved=True)
        elif approval_status == "pending":
            queryset = queryset.filter(is_approved=False)

        return queryset.order_by("-created_at")

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Get context data for model library listing.

        Args:
        ----
            **kwargs: Additional keyword arguments

        Returns:
        -------
            Context dictionary with filters and statistics

        """
        context = super().get_context_data(**kwargs)

        # Add filter options
        context.update(
            {
                "model_types": ThreeDModelLibrary.MODEL_TYPES,
                "utility_categories": ThreeDModelLibrary.UTILITY_CATEGORIES,
                "quality_levels": ThreeDModelLibrary.QUALITY_LEVELS,
                "current_filters": {
                    "search": self.request.GET.get("search", ""),
                    "model_type": self.request.GET.get("model_type", ""),
                    "utility_category": self.request.GET.get("utility_category", ""),
                    "quality_level": self.request.GET.get("quality_level", ""),
                    "approval_status": self.request.GET.get("approval_status", ""),
                },
                "stats": self._get_library_stats(),
            },
        )

        return context

    def _get_library_stats(self) -> dict[str, Any]:
        """Get library statistics"""
        return {
            "total_models": ThreeDModelLibrary.objects.count(),
            "approved_models": ThreeDModelLibrary.objects.filter(is_approved=True).count(),
            "procedural_models": ThreeDModelLibrary.objects.filter(is_procedural=True).count(),
            "avg_file_size": ThreeDModelLibrary.objects.aggregate(avg_size=Avg("file_size_bytes"))["avg_size"] or 0,
        }


@login_required
@user_passes_test(is_3d_admin)
def model_detail_view(request: HttpRequest, model_id: str) -> HttpResponse:
    """Detailed view of a 3D model.

    Displays comprehensive information about a 3D model including
    related symbol mappings, generation jobs, and optimization metrics.

    Args:
    ----
        request: HTTP request object
        model_id: 3D model identifier

    Returns:
    -------
        Rendered model detail page

    """
    model = get_object_or_404(ThreeDModelLibrary, id=model_id)

    # Get related mappings
    symbol_mappings = UtilitySymbol3DMapping.objects.filter(
        Q(primary_model=model) | Q(fallback_model=model),
    ).select_related("primary_model", "fallback_model")

    # Get generation jobs if procedural
    generation_jobs = []
    if model.is_procedural:
        generation_jobs = ModelGenerationJob.objects.filter(generated_model=model).select_related(
            "template",
            "requested_by",
        )

    context = {
        "model": model,
        "symbol_mappings": symbol_mappings,
        "generation_jobs": generation_jobs,
        "file_size_mb": model.get_file_size_mb(),
        "optimization_score": model.get_optimization_score(),
    }

    return render(request, "infrastructure/admin/3d_models/model_detail.html", context)


@login_required
@user_passes_test(is_3d_admin)
@require_http_methods(["POST"])
def approve_model(request: HttpRequest, model_id: str) -> HttpResponse | JsonResponse:
    """Approve a 3D model"""
    model = get_object_or_404(ThreeDModelLibrary, id=model_id)
    model.is_approved = True
    model.save(update_fields=["is_approved"])

    messages.success(request, f'Model "{model.name}" has been approved.')

    # HTMX response or redirect
    if request.headers.get("HX-Request"):
        return JsonResponse({"status": "success", "approved": True})
    return redirect("model_detail", model_id=model_id)


@login_required
@user_passes_test(is_3d_admin)
@require_http_methods(["POST"])
def toggle_model_public(request: HttpRequest, model_id: str) -> HttpResponse | JsonResponse:
    """Toggle model public/private status"""
    model = get_object_or_404(ThreeDModelLibrary, id=model_id)
    model.is_public = not model.is_public
    model.save(update_fields=["is_public"])

    status = "public" if model.is_public else "private"
    messages.success(request, f'Model "{model.name}" is now {status}.')

    if request.headers.get("HX-Request"):
        return JsonResponse({"status": "success", "is_public": model.is_public})
    return redirect("model_detail", model_id=model_id)


# =============================================================================
# SYMBOL MAPPING VIEWS
# =============================================================================


@method_decorator([login_required, user_passes_test(is_3d_admin)], name="dispatch")
class SymbolMappingListView(ListView):
    """List view for symbol-to-3D mappings.

    Provides administrative interface for managing mappings between
    2D utility symbols and their corresponding 3D model representations.
    """

    model = UtilitySymbol3DMapping
    template_name: str = "infrastructure/admin/3d_models/symbol_mapping_list.html"
    context_object_name: str = "mappings"
    paginate_by: int = 25

    def get_queryset(self) -> QuerySet[UtilitySymbol3DMapping]:
        """Get symbol mappings ordered by utility type and priority.

        Returns
        -------
            Queryset of symbol mappings with related models

        """
        return UtilitySymbol3DMapping.objects.select_related("primary_model", "fallback_model").order_by(
            "utility_type",
            "symbol_name",
            "-priority",
        )

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Get context data for symbol mapping listing.

        Args:
        ----
            **kwargs: Additional keyword arguments

        Returns:
        -------
            Context dictionary with mappings grouped by utility type

        """
        context = super().get_context_data(**kwargs)

        # Group mappings by utility type for better display
        mappings_by_type = {}
        for mapping in context["mappings"]:
            if mapping.utility_type not in mappings_by_type:
                mappings_by_type[mapping.utility_type] = []
            mappings_by_type[mapping.utility_type].append(mapping)

        context["mappings_by_type"] = mappings_by_type
        return context


@login_required
@user_passes_test(is_3d_admin)
def create_symbol_mapping(request: HttpRequest) -> HttpResponse:
    """Create a new symbol-to-3D mapping"""
    if request.method == "POST":
        try:
            # Parse form data
            utility_type = request.POST.get("utility_type")
            symbol_name = request.POST.get("symbol_name")
            primary_model_id = request.POST.get("primary_model")

            # Validate required fields
            if not all([utility_type, symbol_name, primary_model_id]):
                messages.error(request, "All required fields must be filled.")
                return redirect("symbol_mapping_list")

            # Get the primary model
            primary_model = get_object_or_404(ThreeDModelLibrary, id=primary_model_id)

            # Create mapping
            mapping = UtilitySymbol3DMapping.objects.create(
                utility_type=utility_type,
                symbol_name=symbol_name,
                symbol_code=request.POST.get("symbol_code", ""),
                primary_model=primary_model,
                fallback_model_id=request.POST.get("fallback_model") or None,
                scale_factor=float(request.POST.get("scale_factor", 1.0)),
                priority=int(request.POST.get("priority", 0)),
                is_active=request.POST.get("is_active") == "on",
            )

            messages.success(request, f"Symbol mapping created: {mapping}")

        except (ConnectionError, TimeoutError, HTTPError) as e:
            messages.error(request, f"Error creating mapping: {e!s}")

    # Get available models for selection
    available_models = ThreeDModelLibrary.objects.filter(is_approved=True)

    context = {
        "available_models": available_models,
        "utility_types": ThreeDModelLibrary.UTILITY_CATEGORIES,
    }

    return render(request, "infrastructure/admin/3d_models/create_symbol_mapping.html", context)


# =============================================================================
# PROCEDURAL GENERATION VIEWS
# =============================================================================


@method_decorator([login_required, user_passes_test(is_3d_admin)], name="dispatch")
class ProceduralTemplateListView(ListView):
    """List view for procedural templates.

    Displays available procedural model generation templates for
    administrative management and model generation workflows.
    """

    model = ProceduralModelTemplate
    template_name: str = "infrastructure/admin/3d_models/procedural_template_list.html"
    context_object_name: str = "templates"
    paginate_by: int = 20

    def get_queryset(self) -> QuerySet[ProceduralModelTemplate]:
        """Get active procedural templates ordered by type and name.

        Returns
        -------
            Queryset of active procedural templates

        """
        return ProceduralModelTemplate.objects.filter(is_active=True).order_by("generation_type", "name")


@login_required
@user_passes_test(is_3d_admin)
def generate_model_view(request: HttpRequest, template_id: str) -> HttpResponse:
    """View for generating a model from a template.

    Handles procedural model generation with parameter configuration,
    optimization profiles, and progress tracking.

    Args:
    ----
        request: HTTP request object with generation parameters
        template_id: Procedural template identifier

    Returns:
    -------
        Rendered generation form or redirect to generated model

    """
    template = get_object_or_404(ProceduralModelTemplate, id=template_id)

    if request.method == "POST":
        try:
            # Parse generation parameters from form
            parameters = {}
            for key, value in request.POST.items():
                if key.startswith("param_"):
                    param_name = key[6:]  # Remove 'param_' prefix

                    # Try to convert to appropriate type
                    try:
                        if "." in value:
                            parameters[param_name] = float(value)
                        else:
                            parameters[param_name] = int(value)
                    except ValueError:
                        parameters[param_name] = value

            # Add utility category if specified
            if request.POST.get("utility_category"):
                parameters["utility_category"] = request.POST.get("utility_category")

            # Get optimization profile
            optimization_profile = None
            profile_id = request.POST.get("optimization_profile")
            if profile_id:
                optimization_profile = ModelOptimizationProfile.objects.get(id=profile_id)

            # Generate the model
            job = procedural_generator.generate_model(
                template=template,
                parameters=parameters,
                user=request.user,
                optimization_profile=optimization_profile,
            )

            if job.status == "completed":
                messages.success(request, f"Model generated successfully: {job.generated_model.name}")
                return redirect("model_detail", model_id=job.generated_model.id)
            messages.error(request, f"Generation failed: {job.error_message}")

        except (FileNotFoundError, PermissionError, OSError) as e:
            messages.error(request, f"Error generating model: {e!s}")

    # Get optimization profiles
    optimization_profiles = ModelOptimizationProfile.objects.filter(is_active=True)

    context = {
        "template": template,
        "parameters": template.parameters or {},
        "constraints": template.constraints or {},
        "optimization_profiles": optimization_profiles,
        "utility_categories": ThreeDModelLibrary.UTILITY_CATEGORIES,
    }

    return render(request, "infrastructure/admin/3d_models/generate_model.html", context)


@method_decorator([login_required, user_passes_test(is_3d_admin)], name="dispatch")
class GenerationJobListView(ListView):
    """List view for model generation jobs.

    Provides monitoring and management interface for procedural model
    generation jobs including status tracking and error diagnostics.
    """

    model = ModelGenerationJob
    template_name: str = "infrastructure/admin/3d_models/generation_job_list.html"
    context_object_name: str = "jobs"
    paginate_by: int = 25

    def get_queryset(self) -> QuerySet[ModelGenerationJob]:
        """Get generation jobs with related data ordered by creation time.

        Returns
        -------
            Queryset of generation jobs with template and user relations

        """
        return ModelGenerationJob.objects.select_related("template", "requested_by", "generated_model").order_by(
            "-created_at",
        )


@login_required
@user_passes_test(is_3d_admin)
def generation_job_detail(request: HttpRequest, job_id: str) -> HttpResponse:
    """Detailed view of a generation job"""
    job = get_object_or_404(
        ModelGenerationJob.objects.select_related("template", "requested_by", "generated_model"),
        id=job_id,
    )

    context = {
        "job": job,
        "parameters_json": json.dumps(job.parameters, indent=2),
        "log_entries": job.generation_log or [],
    }

    return render(request, "infrastructure/admin/3d_models/generation_job_detail.html", context)


# =============================================================================
# API ENDPOINTS FOR HTMX
# =============================================================================


@login_required
@require_http_methods(["GET"])
def model_search_api(request: HttpRequest) -> JsonResponse:
    """API endpoint for searching models (for HTMX autocomplete).

    Provides fast search functionality for model selection in forms
    and administrative interfaces with type filtering support.

    Args:
    ----
        request: HTTP request with search query parameters

    Returns:
    -------
        JSON response with search results limited to 10 items

    """
    query = request.GET.get("q", "").strip()
    model_type = request.GET.get("type", "")

    if not query:
        return JsonResponse({"results": []})

    queryset = ThreeDModelLibrary.objects.filter(is_approved=True)

    # Apply search
    queryset = queryset.filter(Q(name__icontains=query) | Q(description__icontains=query))

    # Apply type filter
    if model_type:
        queryset = queryset.filter(model_type=model_type)

    results = []
    for model in queryset[:10]:  # Limit to 10 results
        results.append(
            {
                "id": str(model.id),
                "name": model.name,
                "type": model.get_model_type_display(),
                "category": model.get_utility_category_display(),
                "thumbnail": model.thumbnail.url if model.thumbnail else None,
            },
        )

    return JsonResponse({"results": results})


@login_required
@user_passes_test(is_3d_admin)
@require_http_methods(["POST"])
def bulk_approve_models(request: HttpRequest) -> JsonResponse:
    """Bulk approve selected models"""
    model_ids = request.POST.getlist("model_ids")

    if not model_ids:
        return JsonResponse({"error": "No models selected"}, status=400)

    updated_count = ThreeDModelLibrary.objects.filter(id__in=model_ids).update(is_approved=True)

    return JsonResponse(
        {
            "success": True,
            "updated_count": updated_count,
            "message": f"{updated_count} models approved.",
        },
    )


@login_required
@user_passes_test(is_3d_admin)
@require_http_methods(["GET"])
def model_stats_api(request: HttpRequest) -> JsonResponse:
    """API endpoint for model statistics dashboard"""
    stats = {
        "total_models": ThreeDModelLibrary.objects.count(),
        "approved_models": ThreeDModelLibrary.objects.filter(is_approved=True).count(),
        "pending_approval": ThreeDModelLibrary.objects.filter(is_approved=False).count(),
        "procedural_models": ThreeDModelLibrary.objects.filter(is_procedural=True).count(),
        "public_models": ThreeDModelLibrary.objects.filter(is_public=True).count(),
        "recent_jobs": ModelGenerationJob.objects.filter(status__in=["processing", "completed"]).count(),
        "failed_jobs": ModelGenerationJob.objects.filter(status="failed").count(),
    }

    # Model type distribution
    type_distribution = {}
    for model_type, display_name in ThreeDModelLibrary.MODEL_TYPES:
        count = ThreeDModelLibrary.objects.filter(model_type=model_type).count()
        if count > 0:
            type_distribution[display_name] = count

    stats["type_distribution"] = type_distribution

    return JsonResponse(stats)
